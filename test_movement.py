#!/usr/bin/env python3
"""
Тест для перевірки управління гравцем
"""

import pygame
import sys
import os

# Додаємо поточну директорію до шляху
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from settings import WIDTH, HEIGHT, FPS
from player import Player

def test_movement():
    """Тестуємо управління гравцем"""
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Тест управління")
    clock = pygame.time.Clock()
    
    player = Player()
    print(f"Початкова позиція гравця: ({player.rect.x}, {player.rect.y})")
    print(f"Гравець живий: {player.alive}")
    print(f"Швидкість гравця: {player.stats['speed']}")
    
    running = True
    frame_count = 0
    
    while running and frame_count < 300:  # Тест на 5 секунд
        clock.tick(FPS)
        frame_count += 1
        
        keys = pygame.key.get_pressed()
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
        
        # Зберігаємо стару позицію
        old_pos = (player.rect.x, player.rect.y)
        
        # Оновлюємо гравця
        player.update(keys)
        
        # Перевіряємо, чи змінилася позиція
        new_pos = (player.rect.x, player.rect.y)
        if old_pos != new_pos:
            print(f"Кадр {frame_count}: Позиція змінилася з {old_pos} на {new_pos}")
        
        # Виводимо інформацію про натиснуті клавіші
        pressed_keys = []
        if keys[pygame.K_w]:
            pressed_keys.append("W")
        if keys[pygame.K_s]:
            pressed_keys.append("S")
        if keys[pygame.K_a]:
            pressed_keys.append("A")
        if keys[pygame.K_d]:
            pressed_keys.append("D")
        
        if pressed_keys:
            print(f"Кадр {frame_count}: Натиснуті клавіші: {', '.join(pressed_keys)}")
            print(f"  Гравець живий: {player.alive}")
            print(f"  Позиція: ({player.rect.x}, {player.rect.y})")
        
        # Малюємо
        screen.fill((0, 0, 0))
        screen.blit(player.image, player.rect)
        pygame.display.flip()
    
    pygame.quit()
    print("Тест завершено")

if __name__ == "__main__":
    test_movement()
