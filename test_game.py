#!/usr/bin/env python3
"""
Тестовий скрипт для перевірки функціональності гри
"""

import pygame
import sys
import os

# Додаємо поточну директорію до шляху
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import *

def test_room_enemies():
    """Тестуємо розстановку ворогів по кімнатах"""
    print("=== Тестування розстановки ворогів ===")
    
    # Тест 1-ї кімнати (пуста)
    spawn_enemies_for_room((1, 1))
    print(f"Кімната (1,1): {len(enemy_group)} ворогів (очікується 0)")
    
    # Тест 2-ї кімнати (2x EnemyStandShoot)
    spawn_enemies_for_room((2, 1))
    print(f"Кімната (2,1): {len(enemy_group)} ворогів (очікується 2)")
    
    # Тест 3-ї кімнати (1x EnemyStandShoot + 1x EnemyCharge)
    spawn_enemies_for_room((2, 2))
    print(f"Кімната (2,2): {len(enemy_group)} ворогів (очікується 2)")
    
    # Тест 4-ї кімнати (2x EnemyStandShoot + 1x EnemyCharge)
    spawn_enemies_for_room((3, 2))
    print(f"Кімната (3,2): {len(enemy_group)} ворогів (очікується 3)")
    
    # Тест 5-ї кімнати (Boss)
    spawn_enemies_for_room((4, 2))
    print(f"Кімната (4,2): {len(enemy_group)} ворогів, босс: {'Так' if boss is not None else 'Ні'}")

def test_room_clearing():
    """Тестуємо логіку очищення кімнат"""
    print("\n=== Тестування очищення кімнат ===")
    
    # Тест кімнати з ворогами
    spawn_enemies_for_room((2, 1))
    current_coords = (2, 1)
    current_room = game_map.rooms[current_coords]
    
    # Перевіряємо чи кімната не очищена з ворогами
    if current_coords == (4, 2):
        current_room.cleared = boss is None
    else:
        current_room.cleared = len(enemy_group) == 0
    
    print(f"Кімната з ворогами очищена: {current_room.cleared} (очікується False)")
    
    # Очищуємо ворогів
    enemy_group.empty()
    
    # Перевіряємо чи кімната очищена без ворогів
    if current_coords == (4, 2):
        current_room.cleared = boss is None
    else:
        current_room.cleared = len(enemy_group) == 0
    
    print(f"Кімната без ворогів очищена: {current_room.cleared} (очікується True)")

def test_obstacles():
    """Тестуємо перешкоди в кімнатах"""
    print("\n=== Тестування перешкод ===")
    
    for room_coords, room in game_map.rooms.items():
        print(f"Кімната {room_coords}: {len(room.obstacle_rects)} перешкод")

if __name__ == "__main__":
    print("Запуск тестів для гри Isaac...")
    
    # Ініціалізуємо pygame (потрібно для створення спрайтів)
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    
    try:
        test_room_enemies()
        test_room_clearing()
        test_obstacles()
        print("\n✅ Всі тести пройшли успішно!")
    except Exception as e:
        print(f"\n❌ Помилка в тестах: {e}")
    finally:
        pygame.quit()
